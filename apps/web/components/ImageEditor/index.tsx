'use client'

import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react'
import { ImageUpload } from './components/ImageUpload'
import { CanvasEditor } from './components/CanvasEditor'
import { useImageProjectManager } from './components/ImageProjectManager'
import type { MaskState } from './components/MaskCanvas'

import { APIConfigModal } from './components/APIConfigModal'
import { ToolPanel } from './components/ToolPanel'
import { ImageThumbnails } from './components/ImageThumbnails'
import { InstructionsModal } from './components/InstructionsModal'
import { useInstructions } from '../../hooks/useInstructions'
import { useImageTransfer } from '../../hooks/useImageTransfer'

import { type AIProvider } from './lib/ai-services'
import { adjustBlurIntensity, canvasToBase64 } from './lib/background-removal'
import {
  removeBackgroundSecure,
  blurBackgroundSecure,
  removeObjectsSecure,
  compositeWithBackgroundSecure,
  base64ToFile,
} from './lib/api-client'
import { blobToBase64 } from './lib/image-utils'
import { DragOverlay } from './components/DragOverlay'
import './index.css'
import { useToast } from '@ui/hooks/use-toast'
import { DEFAULT_NEGATIVE_PROMPT } from './lib/constant'
import UploadProgress from './components/UploadProgress'

export interface ImageData {
  id: string
  file: File
  url: string
  width: number
  height: number
  name: string
  thumbnail?: string
}

export const BASE_URL = 'https://faith1314666-imggen-magic-wand.hf.space'

export const ImageEditor: React.FC = () => {
  const {
    project,
    getCurrentImage,
    getCurrentProcessedUrl,
    getCurrentMaskState,
    getCurrentHistoryState,
    getCurrentProcessingState,
    getCurrentBackgroundRemovedUrl,
    getCurrentOriginalBackgroundRemovedUrl,
    getCurrentBackgroundProcessingState,
    getCurrentBackgroundBlurredUrl,
    getCurrentBackgroundBlurProcessingState,
    getCurrentBackgroundBlurData,
    getCurrentFinalResult,
    setImageProcessingState,
    setImageBackgroundProcessingState,
    setImageBackgroundBlurProcessingState,
    addImage,
    addImages,
    removeImage,
    selectImage,
    saveMaskState,
    saveHistoryState,
    setProcessedResult,
    setBackgroundRemovedResult,
    setBackgroundBlurredResult,
    setBackgroundBlurData,
    replaceBackground,
    clearAllResults,
  } = useImageProjectManager()

  // Ref to access CanvasEditor methods
  const canvasEditorRef = useRef<any>(null)

  const [error, setError] = useState<string | null>(null)
  const [showAPIConfig, setShowAPIConfig] = useState(false)
  const [apiConfig, setApiConfig] = useState({
    provider: 'iopaint' as AIProvider,
    apiKey: '',
    baseUrl: 'https://faith1314666-imggen-magic-wand.hf.space', // 默认值，用户可以在设置中修改
  })
  const [brushSettings, setBrushSettings] = useState({
    size: 20,
    opacity: 100,
    color: '#ff3333',
    shape: 'circle' as import('./components/MagicCursor').CursorShape,
  })

  // Memoize brushSettings to prevent unnecessary re-renders
  const memoizedBrushSettings = useMemo(
    () => ({
      size: brushSettings.size,
      opacity: brushSettings.opacity,
      color: brushSettings.color,
      shape: brushSettings.shape,
    }),
    [
      brushSettings.size,
      brushSettings.opacity,
      brushSettings.color,
      brushSettings.shape,
    ]
  )

  // Global drag and drop state
  const [isGlobalDragOver, setIsGlobalDragOver] = useState(false)

  // Instructions state
  const {
    showInstructions,
    hideInstructions,
    showInstructionsAgain,
    showInstructionsIfFirstTime,
  } = useInstructions()
  const { toast } = useToast()
  // Image transfer state
  const { getTransferredImages, subscribeToImageTransfer } = useImageTransfer()

  // State for simulating upload progress from transferred images
  const [isSimulatingUpload, setIsSimulatingUpload] = useState(false)
  const [simulatedProgress, setSimulatedProgress] = useState<number | null>(
    null
  )

  const finalResult = getCurrentFinalResult()
  const finalUrl = finalResult?.url

  useEffect(() => {
    if (error) {
      toast({
        variant: 'error',
        title: error,
      })
    }
  }, [error])
  // Handle transferred images from HeroSection
  useEffect(() => {
    const handleTransferredImages = () => {
      const transferredImages = getTransferredImages()
      if (transferredImages.length > 0) {
        // Check if we should show progress animation
        const shouldShowProgress = transferredImages.some(
          (img) => img.shouldShowProgress
        )

        if (shouldShowProgress) {
          // 如果需要显示进度，先模拟上传进度动画
          setIsSimulatingUpload(true)
          setSimulatedProgress(0)

          // 模拟进度条动画
          const progressInterval = setInterval(() => {
            setSimulatedProgress((prev) => {
              if (prev === null) return null
              if (prev >= 90) {
                clearInterval(progressInterval)
                return 100
              }
              return prev + 10
            })
          }, 100)

          // 在进度完成后添加图片
          setTimeout(() => {
            clearInterval(progressInterval)
            setSimulatedProgress(100)

            // Convert transferred images to ImageData format
            const imageDataList = transferredImages.map((transferImage) => {
              const imageId = `img_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`
              return {
                id: imageId,
                file: transferImage.file,
                url: transferImage.url,
                width: transferImage.width,
                height: transferImage.height,
                name: transferImage.name,
              }
            })

            // Add images to project
            if (imageDataList.length === 1) {
              addImage(imageDataList[0])
            } else {
              addImages(imageDataList)
            }

            // Show instructions if this is the first upload
            if (project.images.length === 0) {
              showInstructionsIfFirstTime()
            }

            // 清理模拟状态
            setTimeout(() => {
              setIsSimulatingUpload(false)
              setSimulatedProgress(null)
            }, 500)
          }, 1500) // 1.5秒后完成上传
        } else {
          // 直接添加图片，不显示进度
          const imageDataList = transferredImages.map((transferImage) => {
            const imageId = `img_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 11)}`
            return {
              id: imageId,
              file: transferImage.file,
              url: transferImage.url,
              width: transferImage.width,
              height: transferImage.height,
              name: transferImage.name,
            }
          })

          if (imageDataList.length === 1) {
            addImage(imageDataList[0])
          } else {
            addImages(imageDataList)
          }

          if (project.images.length === 0) {
            showInstructionsIfFirstTime()
          }
        }
      }
    }

    // Check for transferred images on mount
    handleTransferredImages()

    // Subscribe to future transfers
    const unsubscribe = subscribeToImageTransfer(handleTransferredImages)

    return unsubscribe
  }, [
    getTransferredImages,
    subscribeToImageTransfer,
    addImage,
    addImages,
    project.images.length,
    showInstructionsIfFirstTime,
  ])

  const handleImageUpload = useCallback(
    (file: File) => {
      setError(null)

      const url = URL.createObjectURL(file)
      const img = new Image()

      img.onload = () => {
        const imageId = `img_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 11)}`
        const newImage: ImageData = {
          id: imageId,
          file,
          url,
          width: img.naturalWidth,
          height: img.naturalHeight,
          name: file.name,
        }

        const isFirstImage = project.images.length === 0

        // Show instructions if this is the first image uploaded
        if (isFirstImage) {
          showInstructionsIfFirstTime()
        }

        addImage(newImage)
      }

      img.onerror = () => {
        setError('Failed to load image. Please try a different file.')
        URL.revokeObjectURL(url)
      }

      img.src = url
    },
    [showInstructionsIfFirstTime, addImage, project.images.length]
  )

  const handleMultipleImageUpload = useCallback(
    (files: File[]) => {
      setError(null)

      const loadPromises = files.map((file, index) => {
        return new Promise<ImageData>((resolve, reject) => {
          const url = URL.createObjectURL(file)
          const img = new Image()

          img.onload = () => {
            const imageId = `img_${Date.now()}_${index}_${Math.random()
              .toString(36)
              .substring(2, 11)}`
            const newImage: ImageData = {
              id: imageId,
              file,
              url,
              width: img.naturalWidth,
              height: img.naturalHeight,
              name: file.name,
            }
            resolve(newImage)
          }

          img.onerror = () => {
            reject(new Error(`Failed to load ${file.name}`))
          }

          img.src = url
        })
      })

      Promise.allSettled(loadPromises).then((results) => {
        const successfulImages: ImageData[] = []
        const failedFiles: string[] = []

        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successfulImages.push(result.value)
          } else {
            failedFiles.push(files[index].name)
          }
        })

        if (successfulImages.length > 0) {
          const isFirstUpload = project.images.length === 0

          // Show instructions if this is the first upload
          if (isFirstUpload) {
            showInstructionsIfFirstTime()
          }

          addImages(successfulImages)
        }

        if (failedFiles.length > 0) {
          setError(`Failed to load: ${failedFiles.join(', ')}`)
        }
      })
    },
    [showInstructionsIfFirstTime, addImages, project.images.length]
  )

  // Global drag and drop handlers
  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Check if dragged items contain files
    if (e.dataTransfer.types.includes('Files')) {
      setIsGlobalDragOver(true)
    }
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Only hide overlay if leaving the main container
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsGlobalDragOver(false)
    }
  }, [])

  const handleGlobalDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsGlobalDragOver(false)

      const files = e.dataTransfer.files
      if (files && files.length > 0) {
        const fileArray = Array.from(files)
        const imageFiles = fileArray.filter((file) =>
          file.type.startsWith('image/')
        )

        if (imageFiles.length === 0) return

        if (imageFiles.length === 1) {
          handleImageUpload(imageFiles[0])
        } else {
          handleMultipleImageUpload(imageFiles)
        }
      }
    },
    [handleImageUpload, handleMultipleImageUpload]
  )

  // Helper function to convert File/Blob to base64 (IOPaint format) - using utility function
  const convertToBase64 = blobToBase64

  const handleProcessImage = useCallback(
    async (maskCanvas: HTMLCanvasElement) => {
      const currentImage = getCurrentImage()
      if (!currentImage) return

      // Check if IOPaint server URL is configured
      if (!apiConfig.baseUrl) {
        setError('Please configure IOPaint server URL in settings')
        setShowAPIConfig(true)
        return
      }

      // Set processing state for current image
      setImageProcessingState(currentImage.id, true)
      setError(null)

      try {
        // Create properly sized mask canvas matching image dimensions
        const maskCanvasForProvider = document.createElement('canvas')
        maskCanvasForProvider.width = currentImage.width
        maskCanvasForProvider.height = currentImage.height
        const maskCtx = maskCanvasForProvider.getContext('2d')!

        // IOPaint expects: white = remove, black = keep
        // First, fill with black background (areas to keep)
        maskCtx.fillStyle = 'black'
        maskCtx.fillRect(
          0,
          0,
          maskCanvasForProvider.width,
          maskCanvasForProvider.height
        )

        // Convert user's colored mask to white mask for IOPaint
        const scaleX = currentImage.width / maskCanvas.width
        const scaleY = currentImage.height / maskCanvas.height

        // Get the user's mask data
        const userMaskCtx = maskCanvas.getContext('2d')!
        const userMaskData = userMaskCtx.getImageData(
          0,
          0,
          maskCanvas.width,
          maskCanvas.height
        )

        // Create a white mask from the user's colored mask
        const whiteMaskCanvas = document.createElement('canvas')
        whiteMaskCanvas.width = maskCanvas.width
        whiteMaskCanvas.height = maskCanvas.height
        const whiteMaskCtx = whiteMaskCanvas.getContext('2d')!

        // Convert any non-transparent pixels to white
        const whiteMaskData = whiteMaskCtx.createImageData(
          maskCanvas.width,
          maskCanvas.height
        )
        for (let i = 0; i < userMaskData.data.length; i += 4) {
          const alpha = userMaskData.data[i + 3]
          if (alpha > 0) {
            // Convert to white with same alpha
            whiteMaskData.data[i] = 255 // R
            whiteMaskData.data[i + 1] = 255 // G
            whiteMaskData.data[i + 2] = 255 // B
            whiteMaskData.data[i + 3] = alpha // A
          } else {
            // Keep transparent
            whiteMaskData.data[i] = 0
            whiteMaskData.data[i + 1] = 0
            whiteMaskData.data[i + 2] = 0
            whiteMaskData.data[i + 3] = 0
          }
        }
        whiteMaskCtx.putImageData(whiteMaskData, 0, 0)

        // Now draw the white mask to the IOPaint canvas
        maskCtx.save()
        maskCtx.scale(scaleX, scaleY)
        maskCtx.globalCompositeOperation = 'source-over'
        maskCtx.drawImage(whiteMaskCanvas, 0, 0)
        maskCtx.restore()

        // Convert image and mask to base64 (IOPaint format)
        // Use background removed image if available, otherwise use original
        const currentBackgroundRemovedUrl = getCurrentBackgroundRemovedUrl()
        let imageBase64: string
        if (finalUrl) {
          const response = await fetch(finalUrl)
          const blob = await response.blob()
          imageBase64 = await convertToBase64(blob)
        } else if (currentBackgroundRemovedUrl) {
          // Convert background removed image URL to base64
          const response = await fetch(currentBackgroundRemovedUrl)
          const blob = await response.blob()
          imageBase64 = await convertToBase64(blob)
        } else {
          // Use original image file
          imageBase64 = await convertToBase64(currentImage.file)
        }
        const maskBlob = await new Promise<Blob>((resolve) => {
          maskCanvasForProvider.toBlob((blob) => resolve(blob!), 'image/png')
        })
        const maskBase64 = await convertToBase64(maskBlob)

        // Convert base64 to proper File objects for the secure API
        const imageFile = base64ToFile(imageBase64, 'image.png', 'image/png')
        const maskFile = base64ToFile(maskBase64, 'mask.png', 'image/png')

        // Use secure API client for object removal
        const resultImageUrl = await removeObjectsSecure(
          {
            image: imageFile,
            mask: maskFile,
            prompt: 'high quality, photorealistic, seamless background',
            negative_prompt: DEFAULT_NEGATIVE_PROMPT,
          },
          apiConfig.baseUrl
        )

        // Store the processed result for the current image
        setProcessedResult(currentImage.id, resultImageUrl)

        // Clear background-related cache after inpaint operation
        // This ensures that subsequent background operations use the new inpaint result
        const currentBackgroundBlurData = getCurrentBackgroundBlurData()
        if (currentBackgroundBlurData) {
          console.log('Clearing background blur cache after inpaint operation')
          setBackgroundBlurData(currentImage.id, null)
        }
      } catch (err) {
        console.warn(
          err instanceof Error
            ? err.message
            : 'Failed to process image. Please try again.'
        )
        setError('Failed to process image. Please try again.')
        console.error('Processing error:', err)
      } finally {
        // Clear processing state for current image
        setImageProcessingState(currentImage.id, false)
      }
    },
    [
      getCurrentImage,
      apiConfig,
      setImageProcessingState,
      getCurrentBackgroundRemovedUrl,
      finalUrl,
      setProcessedResult,
      getCurrentBackgroundBlurData,
      setBackgroundBlurData,
      convertToBase64,
    ]
  )

  const handleRemoveBackground = useCallback(async () => {
    const currentImage = getCurrentImage()
    if (!currentImage) return

    try {
      // Set processing state for current image
      setImageBackgroundProcessingState(currentImage.id, true)
      setError(null)

      // Convert image to base64 for background removal
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        throw new Error('Failed to get canvas context')
      }

      // Load the image (use processed result if available, otherwise use original)
      const currentProcessedUrl = getCurrentProcessedUrl()
      const imageUrl = finalUrl || currentProcessedUrl || currentImage.url

      const img = new Image()
      img.crossOrigin = 'anonymous'

      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = imageUrl
      })

      // Set canvas size to match image
      canvas.width = img.naturalWidth
      canvas.height = img.naturalHeight

      // Draw image to canvas
      ctx.drawImage(img, 0, 0)

      // Convert to base64
      const imageBase64 = canvasToBase64(canvas)

      // Use secure API client for background removal
      const resultBase64 = await removeBackgroundSecure(imageBase64, {
        model: 'u2net', // 可以根据需要调整模型
      })

      // Convert base64 result to data URL
      const resultImageUrl = `data:image/png;base64,${resultBase64}`

      // Store the background removed result for the current image
      setBackgroundRemovedResult(currentImage.id, resultImageUrl)
    } catch (err) {
      console.warn(
        err instanceof Error
          ? err.message
          : 'Failed to remove background. Please try again.'
      )
      setError('Failed to remove background. Please try again.')
      console.error('Background removal error:', err)
    } finally {
      // Clear processing state for current image
      setImageBackgroundProcessingState(currentImage.id, false)
    }
  }, [
    getCurrentImage,
    setImageBackgroundProcessingState,
    getCurrentProcessedUrl,
    finalUrl,
    setBackgroundRemovedResult,
  ])

  const handleReplaceBackground = useCallback(
    async (backgroundUrl: string) => {
      const currentImage = getCurrentImage()
      const currentOriginalBackgroundRemovedUrl =
        getCurrentOriginalBackgroundRemovedUrl()
      const currentBackgroundBlurData = getCurrentBackgroundBlurData()
      const currentBackgroundBlurredUrl = getCurrentBackgroundBlurredUrl()
      const currentProcessedUrl = getCurrentProcessedUrl()

      if (!currentImage) return

      // Check if we need to remove background based on current state
      const needsBackgroundRemoval = (() => {
        // If no background removed result exists, we need to remove background
        if (!currentOriginalBackgroundRemovedUrl) {
          return true
        }

        // If there's a newer inpaint result, check timestamps
        if (currentProcessedUrl) {
          const processedTimestamp =
            project.processedTimestamps[currentImage.id] || 0
          const backgroundRemovedTimestamp =
            project.backgroundRemovedTimestamps[currentImage.id] || 0

          // If inpaint is newer than background removal, we need to remove background again
          if (processedTimestamp > backgroundRemovedTimestamp) {
            console.log(
              'Inpaint result is newer than background removal, need to remove background again'
            )
            return true
          }
        }

        return false
      })()

      let freshForegroundImageUrl = null

      if (needsBackgroundRemoval) {
        console.log('Removing background based on latest image state')

        // Directly perform background removal with the correct image source
        try {
          // Use inpaint result first, then final result, then original
          const sourceImageUrl =
            currentProcessedUrl || finalUrl || currentImage.url
          console.log(
            'Using image source for background removal:',
            sourceImageUrl
          )

          // Set processing state
          setImageBackgroundProcessingState(currentImage.id, true)

          // Convert image to base64 for background removal
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) {
            throw new Error('Failed to get canvas context')
          }

          const img = new Image()
          img.crossOrigin = 'anonymous'

          await new Promise((resolve, reject) => {
            img.onload = resolve
            img.onerror = reject
            img.src = sourceImageUrl
          })

          // Set canvas size to match image
          canvas.width = img.naturalWidth
          canvas.height = img.naturalHeight

          // Draw image to canvas
          ctx.drawImage(img, 0, 0)

          // Convert to base64
          const imageBase64 = canvasToBase64(canvas)

          // Call secure background removal API
          const resultBase64 = await removeBackgroundSecure(imageBase64)

          // Create blob URL for the result
          freshForegroundImageUrl = `data:image/png;base64,${resultBase64}`

          // Store the background removed result for the current image
          setBackgroundRemovedResult(currentImage.id, freshForegroundImageUrl)

          console.log(
            'Background removal completed successfully, using fresh result'
          )
        } catch (err) {
          setError(
            err instanceof Error
              ? err.message
              : 'Failed to remove background. Please try again.'
          )
          console.error('Background removal error:', err)
          return
        } finally {
          setImageBackgroundProcessingState(currentImage.id, false)
        }
      }

      try {
        setError(null)

        // Determine which foreground image to use
        let foregroundImageUrl

        if (freshForegroundImageUrl) {
          // Use the freshly generated foreground image
          console.log('Using fresh foreground image from background removal')
          foregroundImageUrl = freshForegroundImageUrl
        } else if (
          currentBackgroundBlurredUrl &&
          currentBackgroundBlurData &&
          currentBackgroundBlurData.removedBackgroundBase64
        ) {
          // If we have a blurred background result, use the cached pure foreground
          console.log(
            'Using cached foreground from blur data for background replacement'
          )
          foregroundImageUrl = `data:image/png;base64,${currentBackgroundBlurData.removedBackgroundBase64}`
        } else {
          // Fall back to the stored background removed URL
          const updatedOriginalBackgroundRemovedUrl =
            getCurrentOriginalBackgroundRemovedUrl()
          if (!updatedOriginalBackgroundRemovedUrl) {
            setError(
              'No background removed result available. Please remove background first.'
            )
            return
          }
          foregroundImageUrl = updatedOriginalBackgroundRemovedUrl
        }

        // Check if this is the transparent background (same as background removed image)
        if (backgroundUrl === foregroundImageUrl) {
          // For transparent background, directly use the foreground image
          replaceBackground(currentImage.id, backgroundUrl)

          // Clear blur results since we're going back to transparent
          if (currentBackgroundBlurredUrl) {
            setBackgroundBlurData(currentImage.id, null)
          }
          return
        }

        // Use the secure compositeWithBackground function for other backgrounds
        const resultImageUrl = await compositeWithBackgroundSecure(
          foregroundImageUrl,
          backgroundUrl,
          currentImage.width,
          currentImage.height
        )

        // Store the background replaced result for the current image
        replaceBackground(currentImage.id, resultImageUrl)

        // Clear blur results since we've replaced the background
        // The user will need to blur again if they want blur with the new background
        if (currentBackgroundBlurredUrl) {
          console.log('Clearing blur data after background replacement')
          setBackgroundBlurData(currentImage.id, null)
        }
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to replace background. Please try again.'
        )
        console.error('Background replacement error:', err)
      }
    },
    [
      getCurrentImage,
      getCurrentOriginalBackgroundRemovedUrl,
      getCurrentBackgroundBlurData,
      getCurrentBackgroundBlurredUrl,
      getCurrentProcessedUrl,
      replaceBackground,
      setBackgroundBlurData,
      setBackgroundRemovedResult,
      setImageBackgroundProcessingState,
      finalUrl,
      project.processedTimestamps,
      project.backgroundRemovedTimestamps,
    ]
  )

  const handleClearAll = useCallback(() => {
    const currentImage = getCurrentImage()
    if (!currentImage) return

    // Clear all processing results for the current image
    clearAllResults(currentImage.id)
  }, [getCurrentImage, clearAllResults])

  const handleBlurBackground = useCallback(
    async (blurIntensity: number) => {
      const currentImage = getCurrentImage()
      if (!currentImage) return

      try {
        // Set processing state for current image
        setImageBackgroundBlurProcessingState(currentImage.id, true)
        setError(null)

        // Check if we already have blur data cached and if it's still valid
        const existingBlurData = getCurrentBackgroundBlurData()
        const finalResult = getCurrentFinalResult()

        // Determine if cached data is still valid
        const isCacheValid =
          existingBlurData &&
          (() => {
            // If we have a final result, check if it's newer than our cached blur
            if (finalResult.url && finalResult.type !== 'blur') {
              const blurTimestamp =
                project.backgroundBlurredTimestamps[currentImage.id] || 0
              const processedTimestamp =
                project.processedTimestamps[currentImage.id] || 0
              const backgroundRemovedTimestamp =
                project.backgroundRemovedTimestamps[currentImage.id] || 0

              // If any operation is newer than our blur cache, invalidate cache
              const latestOperationTimestamp = Math.max(
                processedTimestamp,
                backgroundRemovedTimestamp
              )
              if (latestOperationTimestamp > blurTimestamp) {
                console.log('Cache invalidated: newer operations detected')
                return false
              }
            }
            return true
          })()

        if (isCacheValid && existingBlurData) {
          // We have valid cached data, just adjust blur intensity (frontend only)
          console.log('Using cached blur data, adjusting intensity only')
          const resultBase64 = await adjustBlurIntensity(
            existingBlurData.originalImageBase64,
            existingBlurData.removedBackgroundBase64,
            blurIntensity
          )

          const resultImageUrl = `data:image/png;base64,${resultBase64}`
          setBackgroundBlurredResult(currentImage.id, resultImageUrl)

          // Update the cached intensity
          setBackgroundBlurData(currentImage.id, {
            ...existingBlurData,
            currentIntensity: blurIntensity,
          })
        } else {
          // First time blur - need to prepare data
          let originalImageBase64: string
          let removedBackgroundBase64: string

          // Convert the source image to base64
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) {
            throw new Error('Failed to get canvas context')
          }

          // Load the image - use the most appropriate source based on operation history
          const currentProcessedUrl = getCurrentProcessedUrl()
          const currentBackgroundRemovedUrl = getCurrentBackgroundRemovedUrl()

          // Get the final result which considers all operations and their timestamps
          const finalResult = getCurrentFinalResult()

          // Determine the best image source for blur background
          let imageUrl: string

          // Priority logic based on what operations have been performed
          if (finalResult.url && finalResult.type === 'final') {
            // Multiple operations have been performed, use the final result
            imageUrl = finalResult.url
            console.log(
              'Using final result for blur background (multiple operations)'
            )
          } else if (currentProcessedUrl) {
            // If we have an inpaint result, use it (preserves object removal)
            imageUrl = currentProcessedUrl
            console.log(
              'Using inpaint result for blur background (preserves object removal)'
            )
          } else if (currentBackgroundRemovedUrl) {
            // Use background removed/replaced result
            imageUrl = currentBackgroundRemovedUrl
            console.log(
              'Using background removed/replaced result for blur background'
            )
          } else {
            // Fall back to original image
            imageUrl = currentImage.url
            console.log('Using original image for blur background')
          }

          console.log('Selected image source for blur background:', imageUrl)
          console.log('Final result type:', finalResult.type)

          const img = new Image()
          img.crossOrigin = 'anonymous'

          await new Promise((resolve, reject) => {
            img.onload = resolve
            img.onerror = reject
            img.src = imageUrl
          })

          // Set canvas size to match image
          canvas.width = img.naturalWidth
          canvas.height = img.naturalHeight

          // Draw image to canvas
          ctx.drawImage(img, 0, 0)

          // Convert to base64
          originalImageBase64 = canvasToBase64(canvas)

          // For the foreground, check if we can reuse existing background-removed result
          const currentOriginalBackgroundRemovedUrl =
            getCurrentOriginalBackgroundRemovedUrl()

          // Determine if we can reuse existing foreground
          const canReuseExistingForeground = (() => {
            if (!currentOriginalBackgroundRemovedUrl) {
              console.log(
                'No existing foreground image, need to remove background'
              )
              return false
            }

            // Check if the source image we're using matches what the foreground was created from
            const currentProcessedUrl = getCurrentProcessedUrl()

            if (imageUrl === currentProcessedUrl) {
              // We're using inpaint result, but foreground might be from original image
              const processedTimestamp =
                project.processedTimestamps[currentImage.id] || 0
              const backgroundRemovedTimestamp =
                project.backgroundRemovedTimestamps[currentImage.id] || 0

              if (processedTimestamp > backgroundRemovedTimestamp) {
                console.log(
                  'Inpaint is newer than foreground, need to remove background from inpaint result'
                )
                return false
              }
            }

            console.log('Can reuse existing foreground image')
            return true
          })()

          if (
            canReuseExistingForeground &&
            currentOriginalBackgroundRemovedUrl
          ) {
            // Reuse existing foreground image
            console.log(
              'Reusing existing foreground image (no API call needed)'
            )
            const response = await fetch(currentOriginalBackgroundRemovedUrl)
            const blob = await response.blob()
            const reader = new FileReader()
            removedBackgroundBase64 = await new Promise((resolve, reject) => {
              reader.onload = () => {
                const result = reader.result as string
                const base64 = result.split(',')[1]
                resolve(base64)
              }
              reader.onerror = reject
              reader.readAsDataURL(blob)
            })
          } else {
            // Need to remove background from the selected image source
            console.log(
              'Removing background from selected image source for foreground (API call required)'
            )
            removedBackgroundBase64 = await removeBackgroundSecure(
              originalImageBase64
            )
          }

          // Apply blur with the prepared data using secure API
          const resultBase64 = await blurBackgroundSecure(
            originalImageBase64,
            removedBackgroundBase64,
            { blurIntensity }
          )

          const resultImageUrl = `data:image/png;base64,${resultBase64}`
          setBackgroundBlurredResult(currentImage.id, resultImageUrl)

          // Cache the data for future adjustments
          setBackgroundBlurData(currentImage.id, {
            originalImageBase64,
            removedBackgroundBase64,
            currentIntensity: blurIntensity,
          })
        }
      } catch (err) {
        console.warn(
          err instanceof Error
            ? err.message
            : 'Failed to blur background. Please try again.'
        )
        setError('Failed to blur background. Please try again.')
        console.error('Background blur error:', err)
      } finally {
        // Clear processing state for current image
        setImageBackgroundBlurProcessingState(currentImage.id, false)
      }
    },
    [
      getCurrentImage,
      setImageBackgroundBlurProcessingState,
      getCurrentProcessedUrl,
      getCurrentBackgroundRemovedUrl,
      getCurrentBackgroundBlurData,
      getCurrentFinalResult,
      getCurrentOriginalBackgroundRemovedUrl,
      setBackgroundBlurredResult,
      setBackgroundBlurData,
      project.backgroundBlurredTimestamps,
      project.backgroundRemovedTimestamps,
      project.processedTimestamps,
    ]
  )

  const handleImageRemove = useCallback(
    (imageId: string) => {
      removeImage(imageId)
    },
    [removeImage]
  )

  // Handle mask state change from CanvasEditor
  const handleMaskStateChange = useCallback(
    (maskState: MaskState) => {
      if (!project.currentImageId) return
      saveMaskState(project.currentImageId, maskState)
    },
    [project.currentImageId, saveMaskState]
  )

  // Handle history state change from CanvasEditor
  const handleHistoryStateChange = useCallback(
    (history: string[], historyIndex: number) => {
      if (!project.currentImageId) return
      saveHistoryState(project.currentImageId, history, historyIndex)
    },
    [project.currentImageId, saveHistoryState]
  )

  const handleImageSelect = useCallback(
    (imageId: string) => {
      selectImage(imageId)
    },
    [selectImage]
  )

  // Handle show help request
  const handleShowHelp = useCallback(() => {
    showInstructionsAgain()
  }, [showInstructionsAgain])

  // Get mask canvas for debug mode
  const getMaskCanvas = useCallback(() => {
    return canvasEditorRef.current?.getMaskCanvas() || null
  }, [])

  const currentImage = getCurrentImage()
  const currentProcessedUrl = getCurrentProcessedUrl()

  return (
    <div className="flex image-editor-container flex-col h-full relative">
      {!currentImage ? (
        <div className="flex-1">
          {isSimulatingUpload ? (
            // 显示模拟上传进度
            <UploadProgress simulatedProgress={simulatedProgress || 0} />
          ) : (
            <ImageUpload
              onImageUpload={handleImageUpload}
              onMultipleImageUpload={handleMultipleImageUpload}
            />
          )}
        </div>
      ) : (
        <div
          className="flex flex-col  relative h-[calc(100vh-112px)] pt-[72px]"
          onDragEnter={handleGlobalDragEnter}
          onDragOver={handleGlobalDragOver}
          onDragLeave={handleGlobalDragLeave}
          onDrop={handleGlobalDrop}
        >
          {/* Global Drag Overlay - only show when there's a current image */}
          {isGlobalDragOver && <DragOverlay />}

          {/* Main Content Area */}
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 flex flex-col bg-white">
              {/* <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-800">
                  Edit Your Image
                </h2>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAPIConfig(true)}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    API Settings
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Upload New Image
                  </Button>
                </div>
              </div> */}

              {/* Canvas Content Area */}
              <div className="flex-1 p-4 pt-[5px]">
                {/* {error && (
                  <Card className="mb-4 p-4 bg-red-50 border-red-200 shadow-sm">
                    <p className="text-red-700">{error}</p>
                  </Card>
                )} */}

                {/* Main Canvas */}
                <CanvasEditor
                  ref={canvasEditorRef}
                  imageData={currentImage}
                  onProcessImage={handleProcessImage}
                  disabled={
                    getCurrentProcessingState() ||
                    getCurrentBackgroundProcessingState() ||
                    getCurrentBackgroundBlurProcessingState()
                  }
                  brushSettings={memoizedBrushSettings}
                  onBrushSettingsChange={setBrushSettings}
                  initialMaskState={getCurrentMaskState()}
                  onMaskStateChange={handleMaskStateChange}
                  initialHistoryState={getCurrentHistoryState()}
                  onHistoryStateChange={handleHistoryStateChange}
                  isProcessing={getCurrentProcessingState()}
                  processedImageUrl={getCurrentProcessedUrl()}
                  finalResult={finalResult}
                  onShowHelp={() => {
                    // This will be handled by CanvasEditor's internal logic
                  }}
                  onRemoveBackground={handleRemoveBackground}
                  isBackgroundProcessing={getCurrentBackgroundProcessingState()}
                  backgroundRemovedImageUrl={getCurrentBackgroundRemovedUrl()}
                  onReplaceBackground={handleReplaceBackground}
                  onBlurBackground={handleBlurBackground}
                  isBackgroundBlurProcessing={getCurrentBackgroundBlurProcessingState()}
                  backgroundBlurredImageUrl={getCurrentBackgroundBlurredUrl()}
                  onClearAll={handleClearAll}
                />
              </div>
            </div>

            {/* Right Side - Tool Panel */}
            <ToolPanel
              brushSettings={memoizedBrushSettings}
              onBrushSettingsChange={setBrushSettings}
              processedImageUrl={currentProcessedUrl}
              backgroundRemovedImageUrl={getCurrentBackgroundRemovedUrl()}
              backgroundBlurredImageUrl={getCurrentBackgroundBlurredUrl()}
              finalResult={finalResult}
              isProcessing={getCurrentProcessingState()}
              isBackgroundProcessing={getCurrentBackgroundProcessingState()}
              isBackgroundBlurProcessing={getCurrentBackgroundBlurProcessingState()}
              disabled={
                getCurrentProcessingState() ||
                getCurrentBackgroundProcessingState() ||
                getCurrentBackgroundBlurProcessingState()
              }
              onShowHelp={handleShowHelp}
              currentImageDimensions={
                currentImage
                  ? {
                      width: currentImage.width,
                      height: currentImage.height,
                    }
                  : undefined
              }
              getMaskCanvas={getMaskCanvas}
              originalImageUrl={currentImage?.url}
            />
          </div>

          {/* Bottom - Image Thumbnails */}
          <ImageThumbnails
            images={project.images}
            currentImageId={project.currentImageId}
            onImageSelect={handleImageSelect}
            onImageAdd={handleImageUpload}
            onMultipleImageAdd={handleMultipleImageUpload}
            onImageRemove={handleImageRemove}
            processedResults={project.processedResults}
            processingStates={project.processingStates}
            backgroundRemovedResults={project.backgroundRemovedResults}
            backgroundProcessingStates={project.backgroundProcessingStates}
            backgroundBlurredResults={project.backgroundBlurredResults}
            backgroundBlurProcessingStates={
              project.backgroundBlurProcessingStates
            }
          />
        </div>
      )}

      <APIConfigModal
        isOpen={showAPIConfig}
        onClose={() => setShowAPIConfig(false)}
        config={apiConfig}
        onConfigChange={setApiConfig}
      />

      <InstructionsModal isOpen={showInstructions} onClose={hideInstructions} />
    </div>
  )
}
