# AutoBeforeAfterSlide 组件

一个自动循环扫过的图片对比组件，基于 BeforeAfterSlide 组件开发，支持自动动画和速度配置。

## 功能特点

- 🔄 **自动循环扫过**：竖线自动从左到右再从右到左循环扫过
- ⚡ **可配置速度**：支持自定义扫过速度
- ⏸️ **悬停暂停**：鼠标悬停时可选择暂停动画
- ✨ **星星闪烁特效**：可开启星星和光效特效，增强视觉吸引力
- 🎨 **视觉效果**：保持原有的毛玻璃效果和标签
- 📱 **响应式设计**：适配各种屏幕尺寸

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `beforeImage` | `string` | - | 前图片 URL（必需） |
| `afterImage` | `string` | - | 后图片 URL（必需） |
| `className` | `string` | `''` | 外层容器的额外 CSS 类名 |
| `beforeAlt` | `string` | `'before'` | 前图片的 alt 属性 |
| `afterAlt` | `string` | `'after'` | 后图片的 alt 属性 |
| `containerClassName` | `string` | `''` | 图片容器的额外 CSS 类名 |
| `hideBlur` | `boolean` | `false` | 是否隐藏毛玻璃背景效果 |
| `speed` | `number` | `1.5` | 扫过速度（秒），完整扫过一次的时间 |
| `pauseOnHover` | `boolean` | `false` | 鼠标悬停时是否暂停动画 |
| `hideLabel` | `boolean` | `false` | 是否隐藏 before/after 标签 |
| `showShine` | `boolean` | `false` | 🆕 是否显示星星闪烁特效 |

## 使用示例

### 基础用法

```tsx
import AutoBeforeAfterSlider from './components/AutoBeforeAfterSlide'

export default function MyComponent() {
  return (
    <div className="w-full h-96">
      <AutoBeforeAfterSlider
        beforeImage="/images/before.jpg"
        afterImage="/images/after.jpg"
        beforeAlt="处理前的图片"
        afterAlt="处理后的图片"
      />
    </div>
  )
}
```

### ✨ 星星闪烁特效

```tsx
<AutoBeforeAfterSlider
  beforeImage="/images/before.jpg"
  afterImage="/images/after.jpg"
  showShine={true} // 开启星星闪烁特效
  speed={2} // 2秒完成一次扫过
  pauseOnHover={true} // 悬停时暂停
/>
```

### 🚀 快速扫过 + 特效

```tsx
<AutoBeforeAfterSlider
  beforeImage="/images/before.jpg"
  afterImage="/images/after.jpg"
  showShine={true}
  speed={1} // 1秒快速扫过
  hideLabel={true} // 隐藏标签
  className="border-2 border-yellow-300/30"
/>
```

### 🌟 完整配置示例

```tsx
<AutoBeforeAfterSlider
  beforeImage="/images/before.jpg"
  afterImage="/images/after.jpg"
  showShine={true}
  speed={3}
  pauseOnHover={true}
  hideLabel={false}
  hideBlur={false}
  className="shadow-lg shadow-yellow-300/20"
  beforeAlt="AI处理前的图片"
  afterAlt="AI处理后的图片"
/>
```

## ✨ 星星闪烁特效详解

### 特效组成

1. **星星元素**：
   - 随机生成 12 个星星和闪光点
   - 两种类型：五角星 ⭐ 和圆形闪光点 ✨
   - 围绕滑块线动态分布

2. **光效特效**：
   - 滑块线上的多层光效
   - 光线扫过动画
   - 中心亮线和光晕效果
   - 脉冲和 ping 动画组合

3. **动画效果**：
   - `animate-twinkle`：星星闪烁动画（2秒）
   - `animate-star-trail`：闪光点轨迹动画（2.5秒）
   - `animate-shine-pulse`：光效脉冲动画（1.5秒）
   - `animate-light-sweep`：光线扫过动画（3秒）

### 自定义样式

组件包含自定义 CSS 动画，位于 `shine-effects.css` 文件中：

```css
@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes star-trail {
  0% { opacity: 0; transform: translateY(10px) scale(0.5); }
  50% { opacity: 1; transform: translateY(0px) scale(1); }
  100% { opacity: 0; transform: translateY(-10px) scale(0.5); }
}
```

## 动画原理

1. 组件使用 `requestAnimationFrame` 实现平滑动画
2. 竖线位置从 0% 到 100% 再回到 0% 循环移动
3. 通过 `clipPath` CSS 属性控制前图片的显示区域
4. 星星特效独立于主动画，有自己的生命周期

## 性能优化

- 使用 `requestAnimationFrame` 确保动画流畅
- 组件卸载时自动清理动画帧
- 悬停暂停时停止位置计算，节省性能
- 星星位置每 4 秒重新生成，避免过度计算

## 适用场景

### 🎯 推荐使用 showShine 的场景：
- 产品功能展示
- AI 效果对比
- 重要特性演示
- 营销页面亮点
- 用户引导界面

### 💡 不推荐使用的场景：
- 文档页面
- 表单界面
- 数据展示页面
- 需要用户专注阅读的内容

## 浏览器兼容性

- 现代浏览器完全支持
- CSS 动画需要支持 `@keyframes`
- 建议在 Chrome 60+、Firefox 55+、Safari 12+ 中使用
