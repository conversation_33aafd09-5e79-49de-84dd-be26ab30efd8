# AutoBeforeAfterSlide 组件

一个自动循环扫过的图片对比组件，基于 BeforeAfterSlide 组件开发，支持自动动画和速度配置。

## 功能特点

- 🔄 **自动循环扫过**：竖线自动从左到右再从右到左循环扫过
- ⚡ **可配置速度**：支持自定义扫过速度
- ⏸️ **悬停暂停**：鼠标悬停时可选择暂停动画
- 🎨 **视觉效果**：保持原有的毛玻璃效果和标签
- 📱 **响应式设计**：适配各种屏幕尺寸

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `beforeImage` | `string` | - | 前图片 URL（必需） |
| `afterImage` | `string` | - | 后图片 URL（必需） |
| `className` | `string` | `''` | 外层容器的额外 CSS 类名 |
| `beforeAlt` | `string` | `'before'` | 前图片的 alt 属性 |
| `afterAlt` | `string` | `'after'` | 后图片的 alt 属性 |
| `containerClassName` | `string` | `''` | 图片容器的额外 CSS 类名 |
| `hideBlur` | `boolean` | `false` | 是否隐藏毛玻璃背景效果 |
| `speed` | `number` | `3` | 扫过速度（秒），完整扫过一次的时间 |
| `pauseOnHover` | `boolean` | `true` | 鼠标悬停时是否暂停动画 |

## 使用示例

### 基础用法

```tsx
import AutoBeforeAfterSlider from './components/AutoBeforeAfterSlide'

export default function MyComponent() {
  return (
    <div className="w-full h-96">
      <AutoBeforeAfterSlider
        beforeImage="/images/before.jpg"
        afterImage="/images/after.jpg"
        beforeAlt="处理前的图片"
        afterAlt="处理后的图片"
      />
    </div>
  )
}
```

### 自定义速度和行为

```tsx
<AutoBeforeAfterSlider
  beforeImage="/images/before.jpg"
  afterImage="/images/after.jpg"
  speed={5} // 5秒完成一次扫过
  pauseOnHover={false} // 悬停时不暂停
  className="border-2 border-blue-500"
  hideBlur={true} // 隐藏毛玻璃效果
/>
```

### 快速扫过效果

```tsx
<AutoBeforeAfterSlider
  beforeImage="/images/before.jpg"
  afterImage="/images/after.jpg"
  speed={1.5} // 1.5秒快速扫过
  pauseOnHover={true}
/>
```

## 动画原理

1. 组件使用 `requestAnimationFrame` 实现平滑动画
2. 竖线位置从 0% 到 100% 再回到 0% 循环移动
3. 通过 `clipPath` CSS 属性控制前图片的显示区域
4. 支持鼠标悬停暂停功能，提升用户体验

## 性能优化

- 使用 `requestAnimationFrame` 确保动画流畅
- 组件卸载时自动清理动画帧
- 悬停暂停时停止位置计算，节省性能

## 样式定制

组件继承了原 BeforeAfterSlide 的所有样式，包括：
- 毛玻璃背景效果
- 圆角边框
- 阴影效果
- 标签样式

可以通过 `className` 和 `containerClassName` 参数进行进一步定制。
