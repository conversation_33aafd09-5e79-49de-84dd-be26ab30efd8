import AutoBeforeAfterSlider from './index'

// 使用示例组件
export default function AutoBeforeAfterSlideExample() {
  return (
    <div className="space-y-8 p-8">
      <h2 className="text-2xl font-bold text-center">AutoBeforeAfterSlide 示例</h2>
      
      {/* 基础示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">基础用法（3秒扫过）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&sat=2&con=1.2"
            beforeAlt="原始图片"
            afterAlt="增强后的图片"
          />
        </div>
      </div>

      {/* 快速扫过示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">快速扫过（1.5秒）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop&sepia=100"
            speed={1.5}
            beforeAlt="彩色森林"
            afterAlt="复古森林"
          />
        </div>
      </div>

      {/* 慢速扫过示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">慢速扫过（6秒，悬停不暂停）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop&bri=-0.3&con=1.5"
            speed={6}
            pauseOnHover={false}
            beforeAlt="明亮风景"
            afterAlt="暗调风景"
          />
        </div>
      </div>

      {/* 无毛玻璃效果示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">无毛玻璃效果</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&hue=180"
            speed={4}
            hideBlur={true}
            beforeAlt="原色调"
            afterAlt="色调转换"
            className="border-2 border-blue-500"
          />
        </div>
      </div>
    </div>
  )
}
