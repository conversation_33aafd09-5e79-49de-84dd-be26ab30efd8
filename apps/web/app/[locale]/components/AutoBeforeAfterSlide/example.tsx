import AutoBeforeAfterSlider from './index'

// 使用示例组件
export default function AutoBeforeAfterSlideExample() {
  return (
    <div className="space-y-8 p-8">
      <h2 className="text-2xl font-bold text-center">AutoBeforeAfterSlide 示例</h2>
      
      {/* 基础示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">基础用法（1.5秒扫过）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&sat=2&con=1.2"
            beforeAlt="原始图片"
            afterAlt="增强后的图片"
          />
        </div>
      </div>

      {/* 星星闪烁特效示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">✨ 星星闪烁特效（showShine=true）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&sat=2&con=1.2"
            beforeAlt="原始图片"
            afterAlt="增强后的图片"
            showShine={true}
            speed={2}
          />
        </div>
      </div>

      {/* 快速扫过 + 星星特效 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🚀 快速扫过 + 星星特效（1秒）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop&sepia=100"
            speed={1}
            showShine={true}
            pauseOnHover={false}
            beforeAlt="彩色森林"
            afterAlt="复古森林"
          />
        </div>
      </div>

      {/* 慢速扫过 + 星星特效 + 隐藏标签 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🌟 慢速扫过 + 星星特效 + 隐藏标签（4秒）</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop&bri=-0.3&con=1.5"
            speed={4}
            showShine={true}
            hideLabel={true}
            pauseOnHover={true}
            beforeAlt="明亮风景"
            afterAlt="暗调风景"
            className="border-2 border-yellow-300/30 shadow-lg shadow-yellow-300/20"
          />
        </div>
      </div>

      {/* 无毛玻璃效果 + 星星特效 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">💫 无毛玻璃效果 + 星星特效</h3>
        <div className="w-full h-96 max-w-2xl mx-auto">
          <AutoBeforeAfterSlider
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&hue=180"
            speed={3}
            showShine={true}
            hideBlur={true}
            beforeAlt="原色调"
            afterAlt="色调转换"
            className="border-2 border-blue-500"
          />
        </div>
      </div>

      {/* 功能说明 */}
      <div className="max-w-4xl mx-auto mt-12 p-6 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-bold mb-4">✨ showShine 特效说明</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold mb-2">🌟 星星效果</h4>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>• 随机生成 12 个星星和闪光点</li>
              <li>• 围绕滑块线动态分布</li>
              <li>• 不同类型的闪烁动画</li>
              <li>• 每 4 秒重新生成位置</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2">💫 光效特效</h4>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>• 滑块线上的多层光效</li>
              <li>• 光线扫过动画</li>
              <li>• 中心亮线和光晕效果</li>
              <li>• 脉冲和 ping 动画组合</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded border-l-4 border-yellow-400">
          <p className="text-sm text-yellow-800">
            <strong>提示：</strong> showShine 特效适合用于突出重要的对比效果，建议在产品展示、功能演示等场景中使用。
            可以与其他参数组合使用，如调整速度、隐藏标签等来获得最佳视觉效果。
          </p>
        </div>
      </div>
    </div>
  )
}
