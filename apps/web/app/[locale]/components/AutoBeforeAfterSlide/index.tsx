'use client'
import { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface AutoBeforeAfterSliderProps {
  beforeImage: string
  afterImage: string
  className?: string
  beforeAlt?: string
  afterAlt?: string
  containerClassName?: string
  hideBlur?: boolean
  speed?: number // 扫过速度，单位为秒，默认为 3 秒
  pauseOnHover?: boolean // 鼠标悬停时是否暂停动画，默认为 true
  hideLabel?: boolean
}

export default function AutoBeforeAfterSlider({
  beforeImage,
  afterImage,
  className = '',
  beforeAlt = 'before',
  afterAlt = 'after',
  containerClassName = '',
  hideBlur = false,
  speed = 1.5,
  pauseOnHover = false,
  hideLabel = false,
}: AutoBeforeAfterSliderProps) {
  const t = useTranslations()
  const [sliderPosition, setSliderPosition] = useState(0)
  const [isHovering, setIsHovering] = useState(false)
  const [direction, setDirection] = useState(1) // 1 为向右，-1 为向左
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>(0)
  const lastTimeRef = useRef<number>(0)

  const handleMouseEnter = () => {
    setIsHovering(true)
  }

  const handleMouseLeave = () => {
    setIsHovering(false)
  }

  // 动画循环函数
  const animate = (currentTime: number) => {
    if (!lastTimeRef.current) {
      lastTimeRef.current = currentTime
    }

    const deltaTime = currentTime - lastTimeRef.current
    lastTimeRef.current = currentTime

    // 如果鼠标悬停且启用了暂停功能，则不更新位置
    if (pauseOnHover && isHovering) {
      animationRef.current = requestAnimationFrame(animate)
      return
    }

    // 计算每毫秒移动的百分比
    const movePerMs = (100 / (speed * 1000)) * direction

    setSliderPosition((prevPosition) => {
      let newPosition = prevPosition + movePerMs * deltaTime

      // 检查边界并改变方向
      if (newPosition >= 100) {
        newPosition = 100
        setDirection(-1)
      } else if (newPosition <= 0) {
        newPosition = 0
        setDirection(1)
      }

      return newPosition
    })

    animationRef.current = requestAnimationFrame(animate)
  }

  useEffect(() => {
    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [speed, direction, isHovering, pauseOnHover])

  return (
    <div
      ref={containerRef}
      className={`relative w-full h-full border border-solid border-white/5 rounded-lg select-none ${className}`}
      style={{ touchAction: 'none' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Container for both images */}
      <div
        className={`relative w-full overflow-hidden h-full rounded-lg ${containerClassName}`}
      >
        {/* After image (full width) with frosted glass effect background */}
        <div className="absolute inset-0 h-full">
          {/* Frosted glass background */}
          {!hideBlur && (
            <>
              <div className="absolute inset-0 bg-gradient-to-br from-gray-100/30 to-white/50 backdrop-blur-xl" />

              <div
                className="absolute inset-0 backdrop-blur-sm"
                style={{
                  backgroundImage: `linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)`,
                  backgroundSize: '20px 20px',
                  backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
                  opacity: 0.1,
                }}
              />
            </>
          )}

          <img
            src={afterImage}
            alt={afterAlt}
            className="relative w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* Before image (clipped) */}
        <div
          className="absolute inset-0"
          style={{
            clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`,
          }}
        >
          <img
            src={beforeImage}
            alt={beforeAlt}
            className="w-full h-full object-cover"
            draggable="false"
          />
        </div>

        {/* Labels */}
        {!hideLabel && (
          <>
            <div className="absolute bottom-4 left-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
              {t('before')}
            </div>
            <div className="absolute bottom-4 right-4 px-3 py-1.5 bg-white/20 backdrop-blur text-black/30 text-sm font-medium rounded-full shadow-sm">
              {t('after')}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
