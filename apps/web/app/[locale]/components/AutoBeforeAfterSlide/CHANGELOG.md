# AutoBeforeAfterSlide 组件更新日志

## v2.0.0 - 2025-01-07

### 🆕 新增功能

#### ✨ 星星闪烁特效 (showShine)
- **新增参数**: `showShine?: boolean` - 控制是否显示星星闪烁特效
- **星星效果**: 随机生成 12 个星星和闪光点，围绕滑块线动态分布
- **光效特效**: 多层光效、光线扫过、中心亮线和光晕效果
- **动画类型**: 
  - 五角星闪烁动画 (2秒周期)
  - 圆形闪光点轨迹动画 (2.5秒周期)
  - 光效脉冲动画 (1.5秒周期)
  - 光线扫过动画 (3秒周期)

#### 🎨 视觉增强
- **自定义 CSS 动画**: 新增 `shine-effects.css` 文件
- **动画关键帧**: `twinkle`, `star-trail`, `shine-pulse`, `light-sweep`
- **颜色方案**: 黄色系光效 (`yellow-300`, `yellow-200`, `yellow-100`)
- **阴影效果**: 添加 `drop-shadow-lg` 和 `shadow-lg` 效果

#### 🔧 技术改进
- **性能优化**: 星星位置每 4 秒重新生成，避免过度计算
- **内存管理**: 组件卸载时清理定时器
- **响应式设计**: 星星分布适配不同屏幕尺寸
- **无障碍支持**: 所有特效元素设置 `pointer-events-none`

### 📋 参数更新

```typescript
interface AutoBeforeAfterSliderProps {
  // ... 原有参数
  showShine?: boolean // 🆕 是否显示星星闪烁特效，默认为 false
}
```

### 🎯 使用示例

```tsx
// 基础用法
<AutoBeforeAfterSlider
  beforeImage="/before.jpg"
  afterImage="/after.jpg"
  showShine={true}
/>

// 完整配置
<AutoBeforeAfterSlider
  beforeImage="/before.jpg"
  afterImage="/after.jpg"
  showShine={true}
  speed={2}
  pauseOnHover={true}
  hideLabel={false}
  className="shadow-lg shadow-yellow-300/20"
/>
```

### 🎨 特效组成

1. **星星元素** (12个):
   - 70% 五角星 ⭐ (SVG)
   - 30% 圆形闪光点 ✨ (div + ping效果)

2. **光效层级**:
   - 主光效层 (黄色渐变 + 脉冲)
   - 光线扫过层 (3秒周期扫过)
   - 中心亮线 (垂直渐变)
   - 光晕效果 (圆形 ping 动画)

3. **动画时序**:
   - 星星: 0-3秒随机延迟
   - 光效: 1.5秒脉冲周期
   - 扫过: 3秒完整周期
   - 重新生成: 4秒间隔

### 🚀 性能指标

- **动画帧率**: 60fps (使用 requestAnimationFrame)
- **内存占用**: 轻量级 (仅在 showShine=true 时加载)
- **CPU 使用**: 优化的动画计算
- **兼容性**: 现代浏览器 (Chrome 60+, Firefox 55+, Safari 12+)

### 📱 响应式支持

- **桌面端**: 完整特效显示
- **平板端**: 自适应星星分布
- **移动端**: 优化的动画性能

### 🎯 适用场景

**推荐使用 showShine 的场景:**
- ✅ 产品功能展示
- ✅ AI 效果对比
- ✅ 营销页面亮点
- ✅ 用户引导界面

**不推荐使用的场景:**
- ❌ 文档页面
- ❌ 表单界面
- ❌ 需要专注阅读的内容

---

## v1.0.0 - 2025-01-06

### 🎉 初始版本

- 基础自动循环扫过功能
- 可配置扫过速度
- 悬停暂停功能
- 毛玻璃背景效果
- 多语言标签支持
- 响应式设计
