'use client'

import { useTranslations } from 'next-intl'
import { Apple, Download, Trophy } from 'lucide-react'
import Image from 'next/image'

// Laurel SVG components using the provided SVG files
const LaurelLeft = ({ className = '' }: { className?: string }) => (
  <div className={className}>
    <Image
      src="/laurel-left.svg"
      alt="Laurel Left"
      width={60}
      height={80}
      className="w-full h-full"
    />
  </div>
)

const LaurelRight = ({ className = '' }: { className?: string }) => (
  <div className={className}>
    <Image
      src="/laurel-right.svg"
      alt="Laurel Right"
      width={60}
      height={80}
      className="w-full h-full"
    />
  </div>
)
const GooglePlay = ({ className }: { className: string }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g fill="none">
        <polygon
          stroke="#4b5563"
          strokeLinejoin="round"
          strokeWidth="2"
          points="3 2 3 22 21 12"
        />
        <path stroke="#4b5563" strokeWidth="2" d="M3,2 L14,16" />
        <path
          stroke="#4b5563"
          strokeWidth="2"
          d="M3,8 L14,22"
          transform="matrix(1 0 0 -1 0 30)"
        />
      </g>
    </svg>
  )
}
export function EditorShowcase() {
  const t = useTranslations('home')
  const companyLogos = [
    {
      image:
        'https://www.aistudios.cn/63da3362f67ed6f71c9489c1/64e5552103160cede15c83c8_microsoft_logo.png',
      className: 'translate-y-[4px]',
    },
    {
      image:
        'https://www.aistudios.cn/63da3362f67ed6f71c9489c1/64e5552163a54a2e3f51062c_Lenovo-Logo%201.png',
    },
    {
      image:
        'https://www.aistudios.cn/6618a0ff31d39d3787770851/67170667baeec5b30f6485dc_ainewscom-logo2.png',
    },
  ]
  const awards = [
    {
      id: 1,
      icon: Apple,
      title: t('editorsChoice'),
      subtitle: 'Apple',
      color: 'text-gray-600',
    },
    {
      id: 2,
      icon: Download,
      title: t('millionDownloads'),
      subtitle: t('downloads'),
      color: 'text-gray-600',
    },
    {
      id: 3,
      icon: GooglePlay,
      title: t('editorsChoice'),
      subtitle: 'Google Play',
      color: 'text-gray-600',
    },
  ]

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('editorShowcaseTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            {t('editorShowcaseDescription')}
          </p>
        </div>

        {/* Awards Showcase - Laurel Wreath Design */}
        <div className="flex flex-wrap justify-center items-center gap-24 mb-16">
          {awards.map((award) => {
            const IconComponent = award.icon
            return (
              <div key={award.id} className="relative group">
                {/* Left Laurel Branch */}

                {/* Award Content */}
                <div className="text-center px-16 py-6 transition-all duration-300 group-hover:scale-105">
                  <div className={`flex justify-center mb-3 ${award.color}`}>
                    <IconComponent className="w-10 h-10 text-gray-600" />
                  </div>
                  <div className={`text-lg font-bold ${award.color} mb-1`}>
                    {award.title}
                  </div>
                  <div className="text-sm text-gray-600">{award.subtitle}</div>
                </div>
                <LaurelLeft
                  className={`absolute -right-2 top-1/2 -translate-y-1/2 h-16 opacity-60 transition-all duration-300 group-hover:opacity-80 group-hover:scale-110`}
                />
                {/* Right Laurel Branch */}
                <LaurelRight
                  className={`absolute -left-2 top-1/2 -translate-y-1/2 h-16 opacity-60 transition-all duration-300 group-hover:opacity-80 group-hover:scale-110`}
                />

                {/* Center dot */}
                <div
                  className={`absolute bottom-2 left-1/2 -translate-x-1/2 w-1.5 h-1.5 rounded-full ${award.color} opacity-70 transition-all duration-300 group-hover:opacity-90`}
                />
              </div>
            )
          })}
        </div>

        {/* Bottom stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center mb-16">
          <div className="space-y-2">
            <div className="text-3xl font-bold text-blue-600">4.9★</div>
            <div className="text-sm text-gray-600">{t('appStoreRating')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-green-600">300M+</div>
            <div className="text-sm text-gray-600">{t('downloads')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-purple-600">50M+</div>
            <div className="text-sm text-gray-600">{t('activeUsers')}</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-orange-600">150+</div>
            <div className="text-sm text-gray-600">{t('countries')}</div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-8">
            {t('trustedByLeadingCompanies')}
          </p>
          <div className="flex items-center gap-8 mx-auto w-fit">
            {companyLogos.map((item, index) => {
              return (
                <img
                  className={`h-8 w-fit ${item.className || ''}`}
                  src={item.image}
                  alt=""
                />
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
