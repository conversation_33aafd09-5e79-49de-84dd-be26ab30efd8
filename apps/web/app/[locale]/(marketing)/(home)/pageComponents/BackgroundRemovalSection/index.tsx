'use client'

import { useState, useRef } from 'react'
import { Upload, ArrowR<PERSON>, Sparkles, Image as ImageIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useImageTransfer } from '../../../../../../hooks/useImageTransfer'

// Animated wireframe lines component
const AnimatedWireframe = ({ isDragActive }: { isDragActive?: boolean }) => {
  return (
    <div
      className={`absolute inset-0 overflow-hidden pointer-events-none transition-all duration-300 ${
        isDragActive ? 'opacity-100 scale-110' : 'opacity-70'
      }`}
    >
      {/* Curved line on the left */}
      <svg
        className="absolute left-4 top-1/2 transform -translate-y-1/2 w-24 h-32"
        viewBox="0 0 100 120"
        fill="none"
      >
        <path
          d="M10 20 Q30 40 20 60 Q10 80 30 100"
          stroke={isDragActive ? '#3B82F6' : '#F59E0B'}
          strokeWidth={isDragActive ? '4' : '3'}
          strokeLinecap="round"
          style={{
            strokeDasharray: '10 5',
            animation: isDragActive
              ? 'dash 1s linear infinite, pulse 1s ease-in-out infinite alternate'
              : 'dash 3s linear infinite, pulse 2s ease-in-out infinite alternate',
          }}
        />
      </svg>

      {/* Wavy line on the right */}
      <svg
        className="absolute right-4 top-1/3 w-20 h-24"
        viewBox="0 0 80 100"
        fill="none"
      >
        <path
          d="M10 10 Q40 30 10 50 Q40 70 10 90"
          stroke={isDragActive ? '#3B82F6' : '#8B5CF6'}
          strokeWidth={isDragActive ? '3' : '2'}
          strokeLinecap="round"
          style={{
            strokeDasharray: '8 4',
            animation: isDragActive
              ? 'dash 1.5s linear infinite reverse, pulse 1s ease-in-out infinite alternate'
              : 'dash 4s linear infinite reverse',
          }}
        />
      </svg>

      {/* Sparkle on the right */}
      <div
        className={`absolute right-8 top-1/4 ${
          isDragActive ? 'animate-spin' : 'animate-bounce'
        }`}
        style={{
          animationDelay: '0.5s',
          animationDuration: isDragActive ? '2s' : '1s',
        }}
      >
        <Sparkles
          className={`w-6 h-6 ${
            isDragActive ? 'text-blue-500' : 'text-yellow-400'
          }`}
        />
      </div>

      {/* Small circle decoration */}
      <div
        className={`absolute right-12 bottom-1/3 w-3 h-3 rounded-full ${
          isDragActive
            ? 'bg-blue-400 animate-bounce'
            : 'bg-pink-400 animate-ping'
        }`}
        style={{
          animationDelay: '1s',
          animationDuration: isDragActive ? '0.5s' : '1s',
        }}
      ></div>

      {/* Plus icon decoration */}
      <div
        className={`absolute left-8 top-1/4 ${
          isDragActive
            ? 'text-blue-500 animate-spin'
            : 'text-blue-400 animate-pulse'
        }`}
        style={{
          animationDelay: '1.5s',
          animationDuration: isDragActive ? '1s' : '2s',
        }}
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      {/* Dotted line decoration */}
      <div
        className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 opacity-60 ${
          isDragActive ? 'animate-bounce' : 'animate-pulse'
        }`}
      ></div>

      {/* Floating geometric shapes */}
      <div
        className={`absolute top-8 left-1/3 w-2 h-2 rotate-45 ${
          isDragActive
            ? 'bg-blue-400 animate-ping'
            : 'bg-green-400 animate-bounce'
        }`}
        style={{
          animationDelay: '2s',
          animationDuration: isDragActive ? '1s' : '3s',
        }}
      ></div>
      <div
        className={`absolute bottom-12 right-1/4 w-1.5 h-1.5 rounded-full ${
          isDragActive
            ? 'bg-blue-400 animate-bounce'
            : 'bg-red-400 animate-ping'
        }`}
        style={{
          animationDelay: '2.5s',
          animationDuration: isDragActive ? '0.8s' : '1s',
        }}
      ></div>
    </div>
  )
}

// Sample images component
const SampleImages = ({
  onImageClick,
  t,
}: {
  onImageClick: (imageUrl: string, index: number) => void
  t: any
}) => {
  const [loadingIndex, setLoadingIndex] = useState<number | null>(null)

  const sampleImages = [
    {
      url: '/images/home/<USER>',
      alt: t('backgroundRemovalSampleBagAlt'),
    },
    {
      url: '/images/home/<USER>',
      alt: t('backgroundRemovalSampleFoxAlt'),
    },
    {
      url: '/images/home/<USER>',
      alt: t('backgroundRemovalSampleCarAlt'),
    },
    {
      url: '/avator/Snipaste_2025-07-04_23-53-58.jpg',
      alt: t('backgroundRemovalSamplePersonAlt'),
    },
  ]

  const handleImageClick = async (imageUrl: string, index: number) => {
    if (loadingIndex !== null) return

    setLoadingIndex(index)
    try {
      await onImageClick(imageUrl, index)
    } finally {
      setTimeout(() => setLoadingIndex(null), 500)
    }
  }

  return (
    <div className="mt-6">
      <p className="text-sm text-gray-600 mb-4 text-center">
        {t('backgroundRemovalNoImage')}
      </p>
      <div className="flex justify-center gap-3">
        {sampleImages.map((image, index) => (
          <button
            key={index}
            onClick={() => handleImageClick(image.url, index)}
            disabled={loadingIndex !== null}
            className={`relative w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
              loadingIndex === index
                ? 'border-blue-500 scale-95'
                : 'border-gray-200 hover:border-blue-400 hover:scale-105'
            }`}
          >
            {loadingIndex === index && (
              <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <img
              src={image.url}
              alt={image.alt}
              className="w-full h-full object-cover"
            />
          </button>
        ))}
      </div>
    </div>
  )
}

export function BackgroundRemovalSection() {
  const t = useTranslations('home')
  const router = useRouter()
  const { setImagesToTransfer, urlToTransferData, useImageUpload } =
    useImageTransfer()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [error, setError] = useState<string | null>(null)

  const {
    isDragActive,
    isUploading,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    handleFileInputChange,
  } = useImageUpload({
    maxFiles: 1,
    onUploadStart: () => setError(null),
    onUploadComplete: (files) => {
      const transferDataWithProgress = files.map((data) => ({
        ...data,
        shouldShowProgress: true,
      }))
      setImagesToTransfer(transferDataWithProgress)
      router.push('/playground')
    },
    onUploadError: (errorMessage) => setError(errorMessage),
  })

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleSampleImageClick = async (imageUrl: string, index: number) => {
    try {
      const transferData = await urlToTransferData(
        imageUrl,
        `sample-${index + 1}.jpg`
      )
      const transferDataWithProgress = {
        ...transferData,
        shouldShowProgress: true,
      }
      setImagesToTransfer([transferDataWithProgress])
      router.push('/playground')
    } catch (error) {
      console.error('Failed to load sample image:', error)
      setError('Failed to load sample image. Please try again.')
    }
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('backgroundRemovalTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            {t('backgroundRemovalDescription')}
          </p>
        </div>

        {/* Main Upload Area */}
        <div className="relative max-w-2xl mx-auto">
          <AnimatedWireframe isDragActive={isDragActive} />

          <div
            className={`relative bg-white rounded-3xl p-12 shadow-xl border-2 border-dashed transition-all duration-300 ${
              isDragActive
                ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 scale-105 shadow-2xl ring-4 ring-blue-200 ring-opacity-50'
                : isUploading
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50/30'
            }`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/png,image/webp"
              onChange={handleFileInputChange}
              className="hidden"
            />

            {/* Drag Overlay */}
            {isDragActive && (
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-3xl flex items-center justify-center z-10 backdrop-blur-sm">
                {/* Ripple effect */}
                <div className="absolute inset-0 rounded-3xl overflow-hidden">
                  <div
                    className="absolute top-1/2 left-1/2 w-4 h-4 bg-blue-400/30 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"
                    style={{ animationDuration: '1s' }}
                  ></div>
                  <div
                    className="absolute top-1/2 left-1/2 w-8 h-8 bg-blue-400/20 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      animationDuration: '1.5s',
                      animationDelay: '0.2s',
                    }}
                  ></div>
                  <div
                    className="absolute top-1/2 left-1/2 w-12 h-12 bg-blue-400/10 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"
                    style={{ animationDuration: '2s', animationDelay: '0.4s' }}
                  ></div>
                </div>

                <div className="text-center space-y-4 animate-bounce relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto shadow-lg relative">
                    {/* Glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-ping opacity-75"></div>
                    <Upload className="w-10 h-10 text-white animate-pulse relative z-10" />
                  </div>
                  <p className="text-xl font-bold text-blue-700 drop-shadow-sm">
                    Drop your image here!
                  </p>
                  <p className="text-sm text-blue-600 drop-shadow-sm">
                    Release to start processing
                  </p>
                </div>
              </div>
            )}

            <div className="text-center">
              {isUploading ? (
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-8 h-8 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <p className="text-lg font-medium text-gray-700">
                    {t('backgroundRemovalProcessing')}
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
                    <ImageIcon className="w-10 h-10 text-white" />
                  </div>

                  <button
                    onClick={handleUploadClick}
                    disabled={isUploading}
                    className="bg-[#0f70e6] hover:!bg-[rgba(18,125,255)] text-white px-10 py-4 rounded-2xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl flex items-center gap-3 mx-auto"
                  >
                    <Upload className="w-5 h-5" />
                    {t('backgroundRemovalUploadText')}
                    <ArrowRight className="w-5 h-5" />
                  </button>

                  <p className="text-gray-600">
                    {t('backgroundRemovalDropText')} <br />
                    <span className="text-sm text-gray-500">
                      {t('backgroundRemovalPasteText')}
                    </span>
                  </p>
                </div>
              )}
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600 text-center">{error}</p>
              </div>
            )}

            <SampleImages onImageClick={handleSampleImageClick} t={t} />

            {/* Privacy Notice */}
            <div className="mt-8 text-center">
              <p className="text-xs text-gray-500">
                {t('backgroundRemovalTerms')}{' '}
                <a
                  href="/legal/terms"
                  className="text-blue-600 hover:underline"
                >
                  Terms of Service
                </a>
                {t('backgroundRemovalPrivacy')}{' '}
                <a
                  href="/legal/privacy-policy"
                  className="text-blue-600 hover:underline"
                >
                  Privacy Policy
                </a>
                .
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -30;
          }
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes glow {
          0%,
          100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
          }
          50% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
              0 0 30px rgba(59, 130, 246, 0.6);
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(4);
            opacity: 0;
          }
        }
      `}</style>
    </section>
  )
}
