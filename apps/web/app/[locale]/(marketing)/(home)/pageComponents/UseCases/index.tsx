'use client'

import { useState, useEffect, ReactNode, useRef } from 'react'
import {
  ArrowRight,
  Play,
  ShoppingBag,
  Users,
  Camera,
  Sparkles,
  Star,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import AutoBeforeAfterSlider from '@/[locale]/components/AutoBeforeAfterSlide'

// Type definition for use case item
interface UseCase {
  id: number
  icon: React.ComponentType<any>
  title: string
  description: string
  cta: string
  image?: string
  stats: string
  isVideo?: boolean
  color: string
  renderContent?: ReactNode // 自定义渲染内容
}

// Simple wireframe elements around use case cards
export function UseCases() {
  const t = useTranslations('home')
  const [activeTab, setActiveTab] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [videoLoading, setVideoLoading] = useState<{ [key: number]: boolean }>(
    {}
  )
  const [videoError, setVideoError] = useState<{ [key: number]: boolean }>({})

  // 视频引用管理
  const videoRefs = useRef<{ [key: number]: HTMLVideoElement | null }>({})
  const [videosLoaded, setVideosLoaded] = useState<{ [key: number]: boolean }>(
    {}
  )

  const useCases: UseCase[] = [
    {
      id: 1,
      icon: Play,
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      cta: t('useCase1CTA'),
      image: 'https://www.media.io/video/video2.mp4',
      stats: t('useCase1Stats'),
      isVideo: true,
      color: 'from-red-500 to-pink-600',
    },
    {
      id: 2,
      icon: ShoppingBag,
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      cta: t('useCase2CTA'),
      image: '/videos/home/<USER>',
      isVideo: true,
      stats: t('useCase2Stats'),
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 3,
      icon: Users,
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      cta: t('useCase3CTA'),
      image:
        'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=600&h=400&fit=crop',
      stats: t('useCase3Stats'),
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 4,
      icon: Camera,
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      cta: t('useCase4CTA'),
      image:
        'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=600&h=400&fit=crop',
      stats: t('useCase4Stats'),
      color: 'from-purple-500 to-indigo-600',
      // 示例：自定义渲染内容
      renderContent: (
        <AutoBeforeAfterSlider
          className="!w-full !h-96 lg:!h-[500px] !object-cover"
          beforeImage="/images/home/<USER>"
          afterImage="/images/home/<USER>"
          beforeAlt="A portrait with a beautiful bg blur effect added by removeAI"
          afterAlt="A portrait with a beautiful bg blur effect added by removeAI"
        />
      ),
    },
  ]

  // Auto-play tabs
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % useCases.length)
    }, 5000) // Change tab every 3 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, useCases.length])

  // 视频控制函数
  const playVideo = (useCaseId: number) => {
    const video = videoRefs.current[useCaseId]
    if (video && videosLoaded[useCaseId]) {
      video.currentTime = 0 // 从头开始播放
      video.play().catch(console.error)
    }
  }

  const pauseVideo = (useCaseId: number) => {
    const video = videoRefs.current[useCaseId]
    if (video) {
      video.pause()
    }
  }

  const handleTabClick = (index: number) => {
    const previousTab = activeTab
    const newTab = index

    // 暂停之前的视频
    if (previousTab !== newTab) {
      const previousUseCase = useCases[previousTab]
      if (previousUseCase?.isVideo) {
        pauseVideo(previousUseCase.id)
      }
    }

    setActiveTab(newTab)
    setIsAutoPlaying(false) // Stop auto-play when user interacts

    // 播放新的视频
    const newUseCase = useCases[newTab]
    if (newUseCase?.isVideo) {
      // 稍微延迟播放，确保 tab 切换动画完成
      setTimeout(() => playVideo(newUseCase.id), 300)
    }

    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  // 监听 activeTab 变化，控制视频播放
  useEffect(() => {
    const currentUseCase = useCases[activeTab]
    if (currentUseCase?.isVideo) {
      // 延迟播放，确保组件渲染完成
      const timer = setTimeout(() => playVideo(currentUseCase.id), 500)
      return () => clearTimeout(timer)
    }
  }, [activeTab, videosLoaded])

  // 组件卸载时清理所有视频
  useEffect(() => {
    return () => {
      // 暂停所有视频
      Object.values(videoRefs.current).forEach((video) => {
        if (video) {
          video.pause()
        }
      })
    }
  }, [])

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 via-white to-slate-50 relative overflow-hidden">
      {/* Minimal background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-15">
        {/* Very subtle grid pattern */}
        <svg
          className="absolute top-0 left-0 w-full h-full"
          viewBox="0 0 1400 1000"
        >
          <defs>
            <pattern
              id="usecases-grid"
              width="100"
              height="100"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 100 0 L 0 0 0 100"
                fill="none"
                stroke="#CBD5E1"
                strokeWidth="0.5"
                opacity="0.2"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#usecases-grid)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section Header */}
        <div className="text-center mb-20 relative">
          {/* Simple top decoration */}
          <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 hidden lg:block">
            <div className="w-16 h-0.5 bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 opacity-50"></div>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('useCasesTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Tab Navigation - SEO Optimized */}
        <nav
          className="flex flex-wrap justify-center gap-4 mb-12"
          role="tablist"
          aria-label="Use Cases"
        >
          {useCases.map((useCase, index) => {
            const IconComponent = useCase.icon
            const isActive = activeTab === index

            return (
              <button
                key={useCase.id}
                onClick={() => handleTabClick(index)}
                role="tab"
                aria-selected={isActive}
                aria-controls={`usecase-panel-${useCase.id}`}
                id={`usecase-tab-${useCase.id}`}
                className={`group relative px-6 py-4 rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 ${
                  isActive
                    ? `bg-gradient-to-r ${useCase.color} text-white shadow-lg scale-105`
                    : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg'
                }`}
              >
                <IconComponent
                  className={`w-5 h-5 ${
                    isActive ? 'text-white' : 'text-gray-500'
                  }`}
                />
                <span>{useCase.cta}</span>

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            )
          })}
        </nav>

        {/* Tab Content - SEO Optimized */}
        <div className="relative">
          {/* All content rendered for SEO, visibility controlled by CSS */}
          {useCases.map((useCase, index) => {
            const isActive = activeTab === index

            return (
              <div
                key={useCase.id}
                id={`usecase-panel-${useCase.id}`}
                role="tabpanel"
                aria-labelledby={`usecase-tab-${useCase.id}`}
                className={`transition-all duration-700 ${
                  isActive
                    ? 'opacity-100 visible relative z-10'
                    : 'opacity-0 invisible absolute inset-0 z-0'
                }`}
                aria-hidden={!isActive}
              >
                {/* Main Media Display */}
                <div className="relative max-w-4xl mx-auto mb-8">
                  <div className="relative rounded-3xl overflow-hidden shadow-2xl bg-white p-2">
                    <div className="relative rounded-2xl overflow-hidden">
                      {/* 如果有自定义渲染内容，优先使用自定义内容 */}
                      {useCase.renderContent ? (
                        <div className="relative">{useCase.renderContent}</div>
                      ) : useCase.isVideo ? (
                        <div className="relative">
                          <video
                            ref={(el) => {
                              videoRefs.current[useCase.id] = el
                            }}
                            src={useCase.image}
                            muted
                            loop
                            playsInline
                            preload="auto" // 预加载视频
                            className="w-full h-96 lg:h-[500px] object-cover"
                            poster={(useCase as any).poster} // Optional poster image
                            onLoadStart={() =>
                              setVideoLoading((prev) => ({
                                ...prev,
                                [useCase.id]: true,
                              }))
                            }
                            onCanPlay={() => {
                              setVideoLoading((prev) => ({
                                ...prev,
                                [useCase.id]: false,
                              }))
                              setVideosLoaded((prev) => ({
                                ...prev,
                                [useCase.id]: true,
                              }))
                            }}
                            onLoadedData={() => {
                              setVideosLoaded((prev) => ({
                                ...prev,
                                [useCase.id]: true,
                              }))
                            }}
                            onError={() => {
                              setVideoLoading((prev) => ({
                                ...prev,
                                [useCase.id]: false,
                              }))
                              setVideoError((prev) => ({
                                ...prev,
                                [useCase.id]: true,
                              }))
                            }}
                          />

                          {/* Video Loading Indicator */}
                          {videoLoading[useCase.id] && (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50">
                              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            </div>
                          )}

                          {/* Video Error Fallback */}
                          {videoError[useCase.id] && (
                            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50">
                              <div className="text-white text-center">
                                <Play className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">Video unavailable</p>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <img
                          src={useCase.image}
                          alt={useCase.title}
                          width={800}
                          height={500}
                          className="w-full h-96 lg:h-[500px] object-cover"
                        />
                      )}

                      {/* Overlay with stats */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                        <div className="absolute bottom-6 left-6 right-6">
                          <div className="flex items-center gap-2 text-white font-semibold bg-black/40 backdrop-blur-sm rounded-lg p-4">
                            <Sparkles className="w-6 h-6" />
                            <span className="text-lg">{useCase.stats}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Below Image */}
                <div className="text-center max-w-4xl mx-auto">
                  <div className="space-y-6">
                    <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
                      {useCase.title}
                    </h3>
                    <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
                      {useCase.description}
                    </p>

                    {/* Stats badge */}
                    <div className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 rounded-full text-base font-medium">
                      <Star className="w-5 h-5 text-yellow-500" />
                      <span className="text-gray-700">{useCase.stats}</span>
                    </div>

                    {/* CTA Button */}
                    <div className="pt-4">
                      <button
                        className={`group bg-gradient-to-r ${useCase.color} hover:shadow-xl text-white px-10 py-5 rounded-2xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 flex items-center gap-3 mx-auto relative overflow-hidden`}
                      >
                        {/* Button shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

                        <span className="relative z-10">{useCase.cta}</span>
                        <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300 relative z-10" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}

          {/* Progress Indicators */}
          <div className="flex justify-center gap-3 mt-12">
            {useCases.map((_, index) => (
              <button
                key={index}
                onClick={() => handleTabClick(index)}
                className={`relative w-12 h-2 rounded-full transition-all duration-300 ${
                  index === activeTab
                    ? 'bg-blue-600'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              >
                {index === activeTab && (
                  <div className="absolute inset-0 bg-blue-600 rounded-full animate-pulse"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -30;
          }
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes glow {
          0%,
          100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
          }
          50% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
              0 0 30px rgba(59, 130, 246, 0.6);
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(4);
            opacity: 0;
          }
        }

        @keyframes pulse-glow {
          0%,
          100% {
            opacity: 0.5;
          }
          50% {
            opacity: 0.8;
          }
        }
      `}</style>
    </section>
  )
}
