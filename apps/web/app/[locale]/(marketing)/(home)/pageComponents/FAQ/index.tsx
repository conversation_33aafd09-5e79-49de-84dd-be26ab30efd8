'use client'

import { useState } from 'react'
import { ChevronDown, Shield, Zap, Star, Clock } from 'lucide-react'
import { useTranslations } from 'next-intl'

export function FAQ() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(1)
  const t = useTranslations('home')

  const faqs = [
    {
      id: 1,
      icon: Shield,
      question: t('faq1Question'),
      answer: t('faq1Answer'),
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 2,
      icon: Zap,
      question: t('faq2Question'),
      answer: t('faq2Answer'),
      color: 'from-purple-500 to-pink-600',
    },
    {
      id: 3,
      icon: Star,
      question: t('faq3Question'),
      answer: t('faq3Answer'),
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 4,
      icon: Clock,
      question: t('faq4Question'),
      answer: t('faq4Answer'),
      color: 'from-orange-500 to-red-600',
    },
  ]

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-4xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('faqTitle')}
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed">
            {t('faqDescription')}
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-6">
          {faqs.map((faq) => {
            const IconComponent = faq.icon
            const isOpen = openFAQ === faq.id

            return (
              <div
                key={faq.id}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full p-6 text-left flex items-center gap-4 hover:bg-gray-50 transition-colors duration-200"
                >
                  {/* Icon */}
                  <div
                    className={`flex-shrink-0 w-12 h-12 bg-gradient-to-r ${faq.color} rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>

                  {/* Question */}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                      {faq.question}
                    </h3>
                  </div>

                  {/* Chevron */}
                  <div
                    className={`flex-shrink-0 transition-transform duration-300 ${
                      isOpen ? 'rotate-180' : ''
                    }`}
                  >
                    <ChevronDown className="w-6 h-6 text-gray-400 group-hover:text-blue-500" />
                  </div>
                </button>

                {/* Answer */}
                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="px-6 pb-6">
                    <div className="pl-16">
                      <div
                        className={`bg-opacity-5 rounded-xl p-4 border-l-4`}
                      >
                        <p className="text-gray-700 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Additional Help Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {t('stillHaveQuestions')}
            </h3>
            <p className="text-gray-600 mb-6">{t('supportTeamHelp')}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-[#0f70e6] hover:!bg-[rgba(18,125,255)] text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105">
                {t('contactSupport')}
              </button>
              <button className="border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-600 px-8 py-3 rounded-xl font-semibold transition-all duration-300">
                {t('viewDocumentation')}
              </button>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {t('secureLabel')}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <Zap className="w-6 h-6 text-blue-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {t('lightningFastLabel')}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {t('topRatedLabel')}
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              {t('availableLabel')}
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
