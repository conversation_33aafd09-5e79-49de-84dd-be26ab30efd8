'use client'

import { useState } from 'react'
import {
  Upload,
  Download,
  ArrowR<PERSON>,
  Clock,
  Zap,
  CheckCircle,
} from 'lucide-react'
import { useTranslations } from 'next-intl'

export function HowToGuide() {
  const t = useTranslations('home')
  const [activeStep, setActiveStep] = useState<number | null>(null)

  const steps = [
    {
      id: 1,
      icon: Upload,
      title: t('step1Title'),
      description: t('step1Description'),
      features: [
        t('step1Feature1'),
        t('step1Feature2'),
        t('step1Feature3'),
        t('step1Feature4'),
      ],
      color: 'from-blue-500 to-cyan-600',
      bgColor: 'from-blue-50 to-cyan-50',
    },
    {
      id: 2,
      icon: Download,
      title: t('step2Title'),
      description: t('step2Description'),
      features: [
        t('step2Feature1'),
        t('step2Feature2'),
        t('step2Feature3'),
        t('step2Feature4'),
      ],
      color: 'from-purple-500 to-pink-600',
      bgColor: 'from-purple-50 to-pink-50',
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('howToGuideTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('howToGuideDescription')}
          </p>
        </div>

        {/* Steps Progress */}
        <div className="max-w-4xl mx-auto mb-16">
          {/* Progress Bar Container */}
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-8 left-0 right-0 h-1 bg-gray-200 rounded-full">
              <div
                className={`h-full rounded-full transition-all duration-700 ${
                  activeStep === 2
                    ? 'w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-600 shadow-lg'
                    : 'w-1/2 bg-gradient-to-r from-blue-500 to-purple-600'
                }`}
              ></div>

              {/* Animated progress indicator */}
              {activeStep === 2 && (
                <div className="absolute top-0 right-0 w-3 h-1 bg-white rounded-full animate-pulse"></div>
              )}
            </div>

            {/* Steps */}
            <div className="relative flex justify-between">
              {steps.map((step, index) => {
                const IconComponent = step.icon
                const isActive = activeStep === step.id
                const isCompleted =
                  index === 0 || (activeStep === 2 && index === 1) // First step is always completed, second step completes on hover

                return (
                  <div
                    key={step.id}
                    className="flex flex-col items-center cursor-pointer group"
                    onMouseEnter={() => setActiveStep(step.id)}
                    onMouseLeave={() => setActiveStep(null)}
                  >
                    {/* Step Circle */}
                    <div
                      className={`relative w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                        isCompleted || isActive
                          ? `bg-gradient-to-r ${step.color} shadow-lg scale-110`
                          : 'bg-white border-4 border-gray-300 group-hover:border-gray-400'
                      }`}
                    >
                      {isCompleted ? (
                        <CheckCircle className="w-8 h-8 text-white" />
                      ) : (
                        <IconComponent
                          className={`w-8 h-8 ${
                            isActive
                              ? 'text-white'
                              : 'text-gray-400 group-hover:text-gray-600'
                          }`}
                        />
                      )}

                      {/* Step Number Badge */}
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center text-xs font-bold text-gray-600">
                        {step.id}
                      </div>
                    </div>

                    {/* Step Content */}
                    <div className="mt-6 text-center max-w-xs">
                      <h3
                        className={`text-lg font-bold mb-2 transition-colors duration-300 ${
                          isActive ? 'text-blue-600' : 'text-gray-900'
                        }`}
                      >
                        {step.title}
                      </h3>
                      <p className="text-sm text-gray-600 leading-relaxed mb-4">
                        {step.description}
                      </p>

                      {/* Features List - Show on hover/active */}
                      <div
                        className={`space-y-2 overflow-hidden transition-all duration-500 ${
                          isActive
                            ? 'max-h-40 opacity-100'
                            : 'max-h-0 opacity-0'
                        }`}
                      >
                        {step.features.map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-center gap-2 text-xs text-gray-500"
                          >
                            <div className="w-1 h-1 bg-blue-500 rounded-full flex-shrink-0"></div>
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Process Flow Visualization */}

        {/* CTA Section */}
        <div className="text-center">
          <button className="group bg-[#0f70e6] hover:!bg-[rgba(18,125,255)] text-white px-12 py-5 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 mx-auto">
            {t('startEditingNowForFree')}
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </button>
          <p className="text-sm text-gray-500 mt-4">{t('noAccountNeeded')}</p>
        </div>
      </div>
    </section>
  )
}
