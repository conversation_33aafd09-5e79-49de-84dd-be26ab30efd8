# Database
DATABASE_URL="YOUR_DATABASE_CONNECTION_STRING"
# ... if you use Supabase
MIGRATION_DATABASE_URL=""

# Site url
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
# 
# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Chargebee
CHARGEBEE_SITE=""
CHARGEBEE_API_KEY=""

# Mailing
# ... with nodemailer
MAIL_HOST=""
MAIL_PORT=""
MAIL_USER=""
MAIL_PASS=""
# ... with Plunk
PLUNK_API_KEY=""

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# 

# Supabase
NEXT_PUBLIC_SUPABASE_URL = "https://jrgocbhuubiqyvrbsqut.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpyZ29jYmh1dWJpcXl2cmJzcXV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwNTY5NjksImV4cCI6MjA0OTYzMjk2OX0.jGJHo_ekbWCmh8kHePVBtboGVEZeA7l98djB-6AebdM"


# Authentication
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="216697875560-m0epur589vlaqfv6dv27e33lmorhgi82.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Storage
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_ENDPOINT=""
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

PAYPRO_VALIDATION_KEY="yB2jNz!QGsRKq7Dnak200MtBXJhhVW"


# AI
# ... with OpenAI
OPENAI_API_KEY=""

ALIYUN_OSS_REGION="oss-ap-southeast-1"
ALIYUN_OSS_ACCESS_KEY_ID="LTAI5tKuTNkbzAQGBCP251oS"
ALIYUN_OSS_ACCESS_KEY_SECRET="******************************"
ALIYUN_OSS_BUCKET="aimakesong-gpt4o-image"
ALIYUN_OSS_ENDPOINT="oss-ap-southeast-1.aliyuncs.com"

# AI AUTH
AI_AUTH_TOKEN="4e6e37c7b625dc75f6b4eef0d271189b"

NEXT_PUBLIC_AB='sb_secret_CQ6W4B7rhID9J0dKrTUGKw_fRZ3bsoJ'



GOOGLE_CLOUD_PROJECT=removeAI-465509

NEXT_PUBLIC_HUGGING_FACE_BASE_URL="https://faith1314666-imggen-magic-wand.hf.space"